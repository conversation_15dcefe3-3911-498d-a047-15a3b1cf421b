import { AreaState } from '@/models/area';
import {
  ModalForm,
  ProFormInstance,
  ProFormText,
  ProFormTreeSelect,
} from '@ant-design/pro-components';
import { connect } from '@umijs/max';
import React, { useRef } from 'react';

type EditModalProps = {
  open: boolean;
  info?: API.Area;
  onSave: (info: API.Area) => Promise<void>;
  onClose: () => void;
  area: AreaState;
};

const EditModal: React.FC<EditModalProps> = ({
  open,
  info,
  onSave,
  onClose,
  area,
}) => {
  const formRef = useRef<ProFormInstance<API.Area>>();

  return (
    <ModalForm<API.Area>
      title={info ? '编辑区域' : '注册区域'}
      formRef={formRef}
      autoFocusFirstInput
      width={400}
      modalProps={{
        destroyOnClose: true,
        onCancel: onClose,
      }}
      open={open}
      layout="horizontal"
      grid
      labelCol={{ flex: '7em' }}
      onFinish={onSave}
      initialValues={info}
    >
      <ProFormTreeSelect
        name="parentCode"
        label="上级区域"
        request={async () => {
          // 只显示两级
          return area.treeData.map((a) => ({
            ...a,
            children: a.children?.map((b) => ({ ...b, children: undefined })),
          }));
        }}
        fieldProps={{
          onChange: (value) => {
            if (info) return null;
            let code = value;
            if (code) {
              while (code.endsWith('00')) {
                code = code.substring(0, code.length - 2);
              }
              formRef.current?.setFieldValue('code', code);
            } else {
              formRef.current?.setFieldValue('code', '');
            }
          },
        }}
      />
      <ProFormText
        name="code"
        label="编号"
        rules={[{ required: true, message: '请输入编号！' }]}
      />
      <ProFormText
        name="name"
        label="名称"
        rules={[{ required: true, message: '请输入名称！' }]}
      />
    </ModalForm>
  );
};

export default connect(({ area }) => ({ area }))(EditModal);
