import {
  ModalForm,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import React from 'react';

type EditModalProps = {
  open: boolean;
  info?: API.Role;
  onSave: (info: API.Role) => Promise<void>;
  onClose: () => void;
};

const EditModal: React.FC<EditModalProps> = ({
  open,
  info,
  onSave,
  onClose,
}) => {
  return (
    <ModalForm<API.Role>
      title={info ? '编辑角色' : '注册角色'}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        onCancel: onClose,
      }}
      open={open}
      layout="horizontal"
      grid
      labelCol={{ flex: '7em' }}
      onFinish={onSave}
      initialValues={info}
    >
      <ProFormText name="id" label="ID" hidden />
      <ProFormText
        name="name"
        label="名称"
        rules={[{ required: true, message: '请输入名称！' }]}
        colProps={{ span: 12 }}
      />
      <ProFormTextArea
        name="description"
        label="描述"
        fieldProps={{ maxLength: 100, showCount: true }}
        initialValue={info?.description || ''}
      />
    </ModalForm>
  );
};

export default EditModal;
